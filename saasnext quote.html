<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Quotation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #1b1e26;
        }
        .container {
            max-width: 900px;
            margin: 40px auto;
            padding: 32px;
            background-color: #1b1e26;
            color: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-[#1b1e26] text-white font-sans">
    <div id="quotation-container" class="container mx-auto p-8 rounded-xl shadow-lg bg-[#1b1e26]">
        <!-- Download Button -->
        <div class="text-right mb-4">
            <button id="downloadButton" class="bg-[#e87121] text-white font-bold py-2 px-4 rounded-lg shadow-md hover:bg-[#e87121]/80 transition-colors">
                Download as Image
            </button>
        </div>

        <!-- Header Section -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white">Comprehensive Project Quotation</h1>
            <p class="text-lg text-white mt-2">Website Redesign for SaaSNext</p>
            <div class="mt-4 flex justify-center">
                <!-- Removed the image and added text instead -->
                <p class="text-3xl font-bold text-[#e87121]">SaaSNext</p>
            </div>
        </div>

        <!-- Tagline Section -->
        <div class="bg-[#1b1e26] p-6 rounded-xl my-8 flex items-center">
            <h2 class="text-2xl font-semibold text-[#e87121] mr-4">Transform Your Digital Presence:</h2>
            <p class="text-lg text-white font-medium">Drive engagement and accelerate growth with a powerful website.</p>
        </div>

        <!-- Quotation Table -->
        <div class="overflow-hidden rounded-xl bg-[#1b1e26] p-6">
            <div class="grid grid-cols-3 gap-4 pb-4 border-b border-gray-700 font-semibold text-white text-lg">
                <div class="col-span-1">COMPONENT</div>
                <div class="col-span-1">DESCRIPTION</div>
                <div class="col-span-1 text-right">BUDGET (₹)</div>
            </div>

            <!-- Table Rows -->
            <div class="grid grid-cols-3 gap-4 py-4 border-b border-gray-700">
                <div class="col-span-1 text-[#e87121]">1. Front-End Design & Development</div>
                <div class="col-span-1">High-end modern UI/UX using Next.js, with custom product, cart, and checkout components</div>
                <div class="col-span-1 text-right">7,500</div>
            </div>
            <div class="grid grid-cols-3 gap-4 py-4 border-b border-gray-700">
                <div class="col-span-1 text-[#e87121]">2. Responsive Design</div>
                <div class="col-span-1">Mobile, tablet & desktop responsiveness with cross-browser testing</div>
                <div class="col-span-1 text-right">2,000</div>
            </div>
            <div class="grid grid-cols-3 gap-4 py-4 border-b border-gray-700">
                <div class="col-span-1 text-[#e87121]">3. Basic SEO Integration</div>
                <div class="col-span-1">Meta tags, Open Graph, structured URLs, image alt tags, sitemap, robots.txt, performance tweaks</div>
                <div class="col-span-1 text-right">2,000</div>
            </div>
            <div class="grid grid-cols-3 gap-4 py-4 border-b border-gray-700">
                <div class="col-span-1 text-[#e87121]">4. Marketplace & Localization Module</div>
                <div class="col-span-1">
                    Includes:
                    <ul class="list-disc list-inside mt-2">
                        <li>Multi-vendor setup</li>
                        <li>Multi-language support</li>
                        <li>Multi-currency support</li>
                    </ul>
                </div>
                <div class="col-span-1 text-right">3,500</div>
            </div>
            <div class="grid grid-cols-3 gap-4 py-4 border-b border-gray-700">
                <div class="col-span-1 text-[#e87121]">5. Project Management & QA</div>
                <div class="col-span-1">Testing, bug fixing, deployment support, cross-device QA</div>
                <div class="col-span-1 text-right">3,000</div>
            </div>

            <!-- Total Row -->
            <div class="grid grid-cols-3 gap-4 pt-6 font-semibold text-white">
                <div class="col-span-1 text-lg">―Total</div>
                <div class="col-span-1"></div>
                <div class="col-span-1 text-right text-2xl">₹18,000</div>
            </div>
        </div>

        <!-- Notes Section -->
        <div class="mt-8 bg-[#1b1e26] p-6 rounded-xl">
            <h3 class="text-xl font-bold text-white mb-4">Notes:</h3>
            <ul class="list-disc list-inside space-y-2 text-white">
                <li>Additional features or scope changes will be quoted separately.</li>
                <li>Payment terms: 30% after initial work completion, followed by 50% of the budget, and then a final 50% upon project completion.</li>
                <li>This quotation is valid for 30 days from the date of issue.</li>
            </ul>
        </div>
    </div>

    <script>
        document.getElementById('downloadButton').addEventListener('click', function() {
            const downloadButton = document.getElementById('downloadButton');
            downloadButton.style.display = 'none';

            const quotationElement = document.getElementById('quotation-container');
            
            // Use a short delay to ensure the button is hidden before the capture
            setTimeout(() => {
                html2canvas(quotationElement).then(function(canvas) {
                    const link = document.createElement('a');
                    link.download = 'project_quotation.png';
                    link.href = canvas.toDataURL('image/png');
                    link.click();
                    link.delete;
                });
                
                // Show the button again after the capture is complete
                downloadButton.style.display = 'block';
            }, 100);
        });
    </script>
</body>
</html>

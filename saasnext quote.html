<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Project Quotation - SaaSNext</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-bg: #0f1419;
            --secondary-bg: #1a1f2e;
            --card-bg: #242b3d;
            --accent-orange: #ff6b35;
            --accent-blue: #4a9eff;
            --text-primary: #ffffff;
            --text-secondary: #a0a9c0;
            --border-color: #2d3748;
            --gradient-start: #ff6b35;
            --gradient-end: #4a9eff;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
            min-height: 100vh;
        }

        .gradient-text {
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .glass-effect {
            background: rgba(36, 43, 61, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .hover-glow:hover {
            box-shadow: 0 0 30px rgba(255, 107, 53, 0.3);
            transform: translateY(-2px);
            transition: all 0.3s ease;
        }

        .table-row:hover {
            background: rgba(255, 107, 53, 0.05);
            transition: all 0.2s ease;
        }

        .animate-fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Custom size specifications - Wide format */
        .a4-container {
            width: 1200px;
            min-height: 800px;
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px;
            box-sizing: border-box;
        }

        /* Print and download specific styles */
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            .download-button {
                display: none !important;
            }
            .a4-container {
                margin: 0;
                box-shadow: none;
            }
        }

        .download-button {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body style="background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);" class="text-white font-sans min-h-screen">
    <!-- Background Pattern -->
    <div class="fixed inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, #ff6b35 0%, transparent 50%), radial-gradient(circle at 75% 75%, #4a9eff 0%, transparent 50%);"></div>
    </div>

    <!-- Download Button - Fixed position outside A4 container -->
    <div class="download-button">
        <button id="downloadButton" class="glass-effect hover-glow px-6 py-3 rounded-xl font-semibold text-white transition-all duration-300 flex items-center gap-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Download A4 Quotation
        </button>
    </div>

    <!-- A4 Container -->
    <div id="quotation-container" class="a4-container animate-fade-in">
        <!-- Main Card -->
        <div class="glass-effect rounded-2xl p-8 shadow-2xl h-full">
            <!-- Header Section -->
            <div class="text-center mb-8">
                <div class="mb-4">
                    <h1 class="text-4xl font-bold gradient-text mb-2">SaaSNext</h1>
                    <div class="w-16 h-1 bg-gradient-to-r from-orange-500 to-blue-500 mx-auto rounded-full"></div>
                </div>
                <h2 class="text-2xl font-bold text-white mb-2">Professional Project Quotation</h2>
                <p class="text-lg text-gray-300 font-medium">Website Redesign & Development Services</p>
                <div class="mt-4 inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm" style="background: rgba(255, 107, 53, 0.1); border: 1px solid rgba(255, 107, 53, 0.2);">
                    <div class="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                    <span class="text-orange-400 font-medium">Ready to Transform</span>
                </div>
            </div>

            <!-- Tagline Section -->
            <div class="glass-effect rounded-xl p-6 mb-6 border-l-4 border-orange-500">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-xl font-bold gradient-text mb-1">Transform Your Digital Presence</h3>
                        <p class="text-base text-gray-300">Drive engagement and accelerate growth with a powerful, modern website solution.</p>
                    </div>
                    <div class="hidden md:block">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-r from-orange-500 to-blue-500 flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quotation Table -->
            <div class="glass-effect rounded-xl p-6 mb-6">
                <h3 class="text-xl font-bold text-white mb-4 flex items-center gap-3">
                    <div class="w-6 h-6 rounded-lg bg-gradient-to-r from-orange-500 to-blue-500 flex items-center justify-center">
                        <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                    </div>
                    Project Breakdown
                </h3>

                <!-- Table Header -->
                <div class="grid grid-cols-12 gap-3 pb-3 mb-4 border-b-2 border-gradient-to-r from-orange-500 to-blue-500">
                    <div class="col-span-1 text-orange-400 font-bold text-xs uppercase tracking-wider">#</div>
                    <div class="col-span-5 text-white font-bold text-xs uppercase tracking-wider">Component</div>
                    <div class="col-span-4 text-white font-bold text-xs uppercase tracking-wider">Description</div>
                    <div class="col-span-2 text-right text-white font-bold text-xs uppercase tracking-wider">Budget (₹)</div>
                </div>

                <!-- Table Rows -->
                <div class="space-y-3">
                    <div class="grid grid-cols-12 gap-3 py-3 table-row rounded-lg px-3">
                        <div class="col-span-1 text-orange-500 font-bold text-base">01</div>
                        <div class="col-span-5">
                            <h4 class="font-semibold text-white mb-1 text-sm">Front-End Design & Development</h4>
                            <div class="flex gap-1">
                                <span class="px-2 py-0.5 bg-blue-500/20 text-blue-400 text-xs rounded-full">Next.js</span>
                                <span class="px-2 py-0.5 bg-orange-500/20 text-orange-400 text-xs rounded-full">Modern UI</span>
                            </div>
                        </div>
                        <div class="col-span-4 text-gray-300 text-xs">High-end modern UI/UX with custom product, cart, and checkout components</div>
                        <div class="col-span-2 text-right">
                            <span class="text-lg font-bold text-white">7,500</span>
                        </div>
                    </div>

                    <div class="grid grid-cols-12 gap-4 py-4 table-row rounded-xl px-4">
                        <div class="col-span-1 text-orange-500 font-bold text-lg">02</div>
                        <div class="col-span-5">
                            <h4 class="font-semibold text-white mb-1">Responsive Design</h4>
                            <div class="flex gap-2">
                                <span class="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full">Mobile First</span>
                                <span class="px-2 py-1 bg-purple-500/20 text-purple-400 text-xs rounded-full">Cross-Browser</span>
                            </div>
                        </div>
                        <div class="col-span-4 text-gray-300 text-sm">Mobile, tablet & desktop responsiveness with comprehensive testing</div>
                        <div class="col-span-2 text-right">
                            <span class="text-2xl font-bold text-white">2,000</span>
                        </div>
                    </div>

                    <div class="grid grid-cols-12 gap-4 py-4 table-row rounded-xl px-4">
                        <div class="col-span-1 text-orange-500 font-bold text-lg">03</div>
                        <div class="col-span-5">
                            <h4 class="font-semibold text-white mb-1">SEO Integration</h4>
                            <div class="flex gap-2">
                                <span class="px-2 py-1 bg-yellow-500/20 text-yellow-400 text-xs rounded-full">SEO Ready</span>
                                <span class="px-2 py-1 bg-red-500/20 text-red-400 text-xs rounded-full">Performance</span>
                            </div>
                        </div>
                        <div class="col-span-4 text-gray-300 text-sm">Meta tags, Open Graph, structured URLs, sitemaps, and performance optimization</div>
                        <div class="col-span-2 text-right">
                            <span class="text-2xl font-bold text-white">2,000</span>
                        </div>
                    </div>

                    <div class="grid grid-cols-12 gap-4 py-4 table-row rounded-xl px-4">
                        <div class="col-span-1 text-orange-500 font-bold text-lg">04</div>
                        <div class="col-span-5">
                            <h4 class="font-semibold text-white mb-1">Marketplace & Localization</h4>
                            <div class="flex gap-2 flex-wrap">
                                <span class="px-2 py-1 bg-indigo-500/20 text-indigo-400 text-xs rounded-full">Multi-vendor</span>
                                <span class="px-2 py-1 bg-pink-500/20 text-pink-400 text-xs rounded-full">Multi-language</span>
                                <span class="px-2 py-1 bg-teal-500/20 text-teal-400 text-xs rounded-full">Multi-currency</span>
                            </div>
                        </div>
                        <div class="col-span-4 text-gray-300 text-sm">Complete marketplace setup with international support and localization features</div>
                        <div class="col-span-2 text-right">
                            <span class="text-2xl font-bold text-white">3,500</span>
                        </div>
                    </div>

                    <div class="grid grid-cols-12 gap-4 py-4 table-row rounded-xl px-4">
                        <div class="col-span-1 text-orange-500 font-bold text-lg">05</div>
                        <div class="col-span-5">
                            <h4 class="font-semibold text-white mb-1">Project Management & QA</h4>
                            <div class="flex gap-2">
                                <span class="px-2 py-1 bg-cyan-500/20 text-cyan-400 text-xs rounded-full">Testing</span>
                                <span class="px-2 py-1 bg-orange-500/20 text-orange-400 text-xs rounded-full">Deployment</span>
                            </div>
                        </div>
                        <div class="col-span-4 text-gray-300 text-sm">Comprehensive testing, bug fixing, deployment support, and quality assurance</div>
                        <div class="col-span-2 text-right">
                            <span class="text-2xl font-bold text-white">3,000</span>
                        </div>
                    </div>
                </div>

                <!-- Total Section -->
                <div class="mt-8 pt-6 border-t-2 border-gradient-to-r from-orange-500 to-blue-500">
                    <div class="grid grid-cols-12 gap-4 items-center">
                        <div class="col-span-10">
                            <div class="flex items-center gap-4">
                                <div class="w-12 h-12 rounded-full bg-gradient-to-r from-orange-500 to-blue-500 flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="text-2xl font-bold gradient-text">Total Project Investment</h4>
                                    <p class="text-gray-400">Complete website transformation package</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-span-2 text-right">
                            <div class="text-4xl font-bold gradient-text">₹18,000</div>
                            <div class="text-sm text-gray-400 mt-1">All inclusive</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="grid grid-cols-3 gap-6 mb-6">
                <!-- Payment Terms -->
                <div class="glass-effect rounded-2xl p-6">
                    <h3 class="text-xl font-bold text-white mb-4 flex items-center gap-3">
                        <div class="w-6 h-6 rounded-lg bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center">
                            <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                        Payment Structure
                    </h3>
                    <div class="space-y-3">
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 rounded-full bg-orange-500/20 text-orange-400 flex items-center justify-center font-bold text-sm">1</div>
                            <div>
                                <p class="text-white font-medium">30% Initial Payment</p>
                                <p class="text-gray-400 text-sm">Project kickoff & setup</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 rounded-full bg-blue-500/20 text-blue-400 flex items-center justify-center font-bold text-sm">2</div>
                            <div>
                                <p class="text-white font-medium">50% Milestone Payment</p>
                                <p class="text-gray-400 text-sm">Development completion</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 rounded-full bg-green-500/20 text-green-400 flex items-center justify-center font-bold text-sm">3</div>
                            <div>
                                <p class="text-white font-medium">20% Final Payment</p>
                                <p class="text-gray-400 text-sm">Project delivery & launch</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Project Timeline -->
                <div class="glass-effect rounded-2xl p-6">
                    <h3 class="text-xl font-bold text-white mb-4 flex items-center gap-3">
                        <div class="w-6 h-6 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                            <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        Project Timeline
                    </h3>
                    <div class="space-y-3">
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 rounded-full bg-purple-500/20 text-purple-400 flex items-center justify-center font-bold text-sm">1</div>
                            <div>
                                <p class="text-white font-medium">Week 1-2</p>
                                <p class="text-gray-400 text-sm">Design & Planning Phase</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 rounded-full bg-pink-500/20 text-pink-400 flex items-center justify-center font-bold text-sm">2</div>
                            <div>
                                <p class="text-white font-medium">Week 3-5</p>
                                <p class="text-gray-400 text-sm">Development & Integration</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 rounded-full bg-cyan-500/20 text-cyan-400 flex items-center justify-center font-bold text-sm">3</div>
                            <div>
                                <p class="text-white font-medium">Week 6</p>
                                <p class="text-gray-400 text-sm">Testing & Launch</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Key Features -->
                <div class="glass-effect rounded-2xl p-6">
                    <h3 class="text-xl font-bold text-white mb-4 flex items-center gap-3">
                        <div class="w-6 h-6 rounded-lg bg-gradient-to-r from-green-500 to-teal-500 flex items-center justify-center">
                            <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        Key Features
                    </h3>
                    <div class="space-y-2">
                        <div class="flex items-center gap-2">
                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            <p class="text-gray-300 text-sm">Modern UI/UX Design</p>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <p class="text-gray-300 text-sm">Mobile Responsive</p>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-2 h-2 bg-orange-500 rounded-full"></div>
                            <p class="text-gray-300 text-sm">SEO Optimized</p>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                            <p class="text-gray-300 text-sm">Multi-language Support</p>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-2 h-2 bg-pink-500 rounded-full"></div>
                            <p class="text-gray-300 text-sm">Quality Assurance</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Terms & Conditions -->
            <div class="glass-effect rounded-2xl p-6">
                <h3 class="text-xl font-bold text-white mb-4 flex items-center gap-3">
                    <div class="w-6 h-6 rounded-lg bg-gradient-to-r from-yellow-500 to-orange-500 flex items-center justify-center">
                        <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    Terms & Conditions
                </h3>
                <div class="grid grid-cols-2 gap-4">
                    <div class="space-y-3">
                        <div class="flex items-start gap-2">
                            <div class="w-2 h-2 bg-orange-500 rounded-full mt-1.5 flex-shrink-0"></div>
                            <p class="text-gray-300 text-sm">Additional features quoted separately</p>
                        </div>
                        <div class="flex items-start gap-2">
                            <div class="w-2 h-2 bg-blue-500 rounded-full mt-1.5 flex-shrink-0"></div>
                            <p class="text-gray-300 text-sm">Valid for 30 days from issue date</p>
                        </div>
                        <div class="flex items-start gap-2">
                            <div class="w-2 h-2 bg-green-500 rounded-full mt-1.5 flex-shrink-0"></div>
                            <p class="text-gray-300 text-sm">2 weeks post-launch support included</p>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-start gap-2">
                            <div class="w-2 h-2 bg-purple-500 rounded-full mt-1.5 flex-shrink-0"></div>
                            <p class="text-gray-300 text-sm">Source code delivered on final payment</p>
                        </div>
                        <div class="flex items-start gap-2">
                            <div class="w-2 h-2 bg-pink-500 rounded-full mt-1.5 flex-shrink-0"></div>
                            <p class="text-gray-300 text-sm">Regular progress updates included</p>
                        </div>
                        <div class="flex items-start gap-2">
                            <div class="w-2 h-2 bg-cyan-500 rounded-full mt-1.5 flex-shrink-0"></div>
                            <p class="text-gray-300 text-sm">Professional documentation provided</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="text-center mt-10 pt-8 border-t border-gray-700">
                <p class="text-gray-400 mb-2">Ready to get started?</p>
                <div class="flex justify-center gap-4">
                    <div class="px-6 py-3 bg-gradient-to-r from-orange-500 to-blue-500 rounded-xl text-white font-semibold">
                        Let's Build Something Amazing Together
                    </div>
                </div>
                <p class="text-gray-500 text-sm mt-4">Generated on: <span id="currentDate"></span></p>
            </div>
        </div>
    </div>

    <script>
        // Set current date
        document.addEventListener('DOMContentLoaded', function() {
            const currentDate = new Date().toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            document.getElementById('currentDate').textContent = currentDate;
        });

        // Enhanced download functionality
        document.getElementById('downloadButton').addEventListener('click', function() {
            const downloadButton = document.getElementById('downloadButton');
            const originalText = downloadButton.innerHTML;

            // Show loading state
            downloadButton.innerHTML = `
                <svg class="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Generating...
            `;
            downloadButton.disabled = true;

            const quotationElement = document.getElementById('quotation-container');

            // Use a short delay to ensure the button state is updated
            setTimeout(() => {
                html2canvas(quotationElement, {
                    backgroundColor: '#0f1419',
                    scale: 2, // Higher quality
                    useCORS: true,
                    allowTaint: true,
                    width: 1200, // Fixed width
                    height: 800, // Fixed medium height
                    scrollX: 0,
                    scrollY: 0
                }).then(function(canvas) {
                    const link = document.createElement('a');
                    link.download = 'SaaSNext_Project_Quotation_Wide.png';
                    link.href = canvas.toDataURL('image/png', 1.0);
                    link.click();

                    // Reset button state
                    downloadButton.innerHTML = originalText;
                    downloadButton.disabled = false;
                }).catch(function(error) {
                    console.error('Error generating image:', error);
                    downloadButton.innerHTML = originalText;
                    downloadButton.disabled = false;
                    alert('Error generating image. Please try again.');
                });
            }, 200);
        });

        // Add smooth scrolling and animations
        document.addEventListener('DOMContentLoaded', function() {
            // Animate elements on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Observe all animated elements
            document.querySelectorAll('.table-row, .glass-effect').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                el.style.transition = 'all 0.6s ease-out';
                observer.observe(el);
            });
        });
    </script>
</body>
</html>

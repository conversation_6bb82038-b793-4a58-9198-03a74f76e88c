<?php
/**
 * Plugin Name: Deepak Latest Posts
 * Description: Display latest blog posts in a beautiful responsive card grid using shortcode [latest_posts].
 * Version: 1.1
 * Author: <PERSON><PERSON>gada
 */

if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly

// Shortcode: [latest_posts posts="6" columns="3"]
function deepak_latest_posts_shortcode( $atts ) {
    $a = shortcode_atts( array(
        'posts'   => 6,
        'columns' => 3,
    ), $atts, 'latest_posts' );

    $query = new WP_Query( array(
        'post_type'      => 'post',
        'posts_per_page' => intval( $a['posts'] ),
        'orderby'        => 'date',
        'order'          => 'DESC',
    ) );

    ob_start(); ?>

    <div class="deepak-post-grid">
      <?php if ( $query->have_posts() ) : ?>
        <?php while ( $query->have_posts() ) : $query->the_post(); ?>
          <article class="deepak-card">
            <a href="<?php the_permalink(); ?>" class="deepak-thumb">
              <?php if ( has_post_thumbnail() ) : ?>
                <?php the_post_thumbnail( 'large' ); ?>
              <?php else: ?>
                <img src="https://via.placeholder.com/800x450" alt="<?php the_title(); ?>">
              <?php endif; ?>
            </a>
            <div class="deepak-content">
              <div class="deepak-meta">
                <span class="deepak-date"><?php echo get_the_date(); ?></span>
                <span class="deepak-author">• <?php the_author(); ?></span>
              </div>
              <h3 class="deepak-title">
                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
              </h3>
              <p class="deepak-excerpt"><?php echo wp_trim_words( get_the_excerpt(), 22, '...' ); ?></p>
              <a href="<?php the_permalink(); ?>" class="deepak-readmore">Read More →</a>
            </div>
          </article>
        <?php endwhile; wp_reset_postdata(); ?>
      <?php else: ?>
        <p>No posts found.</p>
      <?php endif; ?>
    </div>

    <style>
      /* ---------- Responsive Grid ---------- */
      .deepak-post-grid {
        display: grid;
        grid-template-columns: repeat(<?php echo intval($a['columns']); ?>, 1fr);
        gap: 24px;
        margin: 20px 0;
      }
      @media (max-width: 1024px) {
        .deepak-post-grid { grid-template-columns: repeat(2, 1fr); }
      }
      @media (max-width: 640px) {
        .deepak-post-grid { grid-template-columns: 1fr; }
      }

      /* ---------- Card Style ---------- */
      .deepak-card {
        background: #fff;
        border-radius: 16px;
        overflow: hidden;
        border: 1px solid #eee;
        box-shadow: 0 8px 20px rgba(0,0,0,0.06);
        display: flex;
        flex-direction: column;
        transition: all 0.3s ease;
      }
      .deepak-card:hover {
        transform: translateY(-6px);
        box-shadow: 0 14px 32px rgba(0,0,0,0.12);
      }

      /* ---------- Thumbnail ---------- */
      .deepak-thumb img {
        width: 100%;
        height: auto;
        aspect-ratio: 16/9;
        object-fit: cover;
        transition: transform 0.4s ease;
      }
      .deepak-card:hover .deepak-thumb img {
        transform: scale(1.05);
      }

      /* ---------- Content ---------- */
      .deepak-content {
        padding: 18px 20px;
        display: flex;
        flex-direction: column;
        flex-grow: 1;
      }

      /* Meta */
      .deepak-meta {
        font-size: 0.85rem;
        color: #666;
        margin-bottom: 8px;
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
      }

      /* Title */
      .deepak-title {
        font-size: 1.2rem;
        margin: 0.5rem 0;
        line-height: 1.4;
      }
      .deepak-title a {
        color: #111;
        text-decoration: none;
      }
      .deepak-title a:hover {
        color: #ff6600;
        text-decoration: underline;
      }

      /* Excerpt */
      .deepak-excerpt {
        color: #444;
        font-size: 0.95rem;
        line-height: 1.5;
        margin-bottom: 12px;
        flex-grow: 1;
      }

      /* Read More Button */
      .deepak-readmore {
        font-weight: 600;
        color: #ff6600;
        text-decoration: none;
        align-self: flex-start;
        transition: color 0.2s ease;
      }
      .deepak-readmore:hover {
        color: #cc5200;
        text-decoration: underline;
      }
    </style>

    <?php
    return ob_get_clean();
}
add_shortcode( 'latest_posts', 'deepak_latest_posts_shortcode' );
